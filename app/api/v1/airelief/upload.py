"""
文件上传API接口
提供通用的文件上传和访问功能
"""
import os
from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends
from fastapi.responses import FileResponse
from app.utils.file_upload import file_upload_manager
from app.schemas.base import Success, Fail
from app.core.dependency import DependAIReliefAuth
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/upload", summary="通用文件上传接口")
async def upload_file(
    file: UploadFile = File(..., description="要上传的文件"),
    file_type: str = Form(..., description="文件类型: audio(音频) 或 avatar(头像)"),
    current_user = Depends(DependAIReliefAuth)
):
    """
    通用文件上传接口
    
    支持的文件类型:
    - audio: 音频文件 (.webm, .wav, .mp3, .m4a)，最大10MB
    - avatar: 头像图片 (.jpg, .jpeg, .png, .gif)，最大5MB
    
    返回文件访问URL和文件信息
    """
    try:
        user_id = current_user.user_id if current_user else None
        logger.info(f"开始上传文件: {file.filename}, 类型: {file_type}, 用户: {user_id}")

        # 保存文件
        file_info = await file_upload_manager.save_file(file, file_type, user_id)
        
        return Success(
            data={
                "file_url": file_info["file_url"],
                "filename": file_info["filename"],
                "original_filename": file_info["original_filename"],
                "file_size": file_info["file_size"],
                "file_type": file_info["file_type"],
                "mime_type": file_info["mime_type"]
            },
            msg="文件上传成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        return Fail(msg=f"文件上传失败: {str(e)}")


@router.get("/files/{file_type}/{filename}", summary="文件访问接口")
async def get_file(
    file_type: str,
    filename: str
):
    """
    文件访问接口
    
    通过文件类型和文件名获取文件
    支持的文件类型: audio, avatar
    """
    try:
        # 验证文件类型
        file_upload_manager.validate_file_type(file_type)
        
        # 检查文件是否存在
        if not file_upload_manager.file_exists(file_type, filename):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 获取文件路径
        file_path = file_upload_manager.get_file_path(file_type, filename)
        
        # 获取MIME类型
        import mimetypes
        mime_type, _ = mimetypes.guess_type(file_path)
        
        logger.info(f"访问文件: {file_path}")
        
        return FileResponse(
            path=file_path,
            media_type=mime_type,
            filename=filename
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件访问失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件访问失败: {str(e)}")


@router.delete("/files/{file_type}/{filename}", summary="删除文件接口")
async def delete_file(
    file_type: str,
    filename: str,
    current_user = Depends(DependAIReliefAuth)
):
    """
    删除文件接口
    
    删除指定的文件
    需要用户认证
    """
    try:
        # 验证文件类型
        file_upload_manager.validate_file_type(file_type)
        
        # 检查文件是否存在
        if not file_upload_manager.file_exists(file_type, filename):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        # 删除文件
        success = file_upload_manager.delete_file(file_type, filename)
        
        if success:
            user_id = current_user.user_id if current_user else "unknown"
            logger.info(f"文件删除成功: {filename}, 用户: {user_id}")
            return Success(msg="文件删除成功")
        else:
            return Fail(msg="文件删除失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件删除失败: {e}")
        return Fail(msg=f"文件删除失败: {str(e)}")


@router.get("/upload/config", summary="获取上传配置信息")
async def get_upload_config():
    """
    获取文件上传配置信息
    
    返回各种文件类型的限制和支持格式
    """
    try:
        config_info = {}
        
        for file_type, config in file_upload_manager.file_type_configs.items():
            config_info[file_type] = {
                "description": config["description"],
                "max_size": config["max_size"],
                "max_size_mb": round(config["max_size"] / (1024 * 1024), 1),
                "allowed_formats": config["allowed_formats"]
            }
        
        return Success(
            data={
                "file_types": config_info,
                "base_url": file_upload_manager.file_type_configs
            },
            msg="获取配置成功"
        )
        
    except Exception as e:
        logger.error(f"获取上传配置失败: {e}")
        return Fail(msg=f"获取配置失败: {str(e)}")
