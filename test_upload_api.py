#!/usr/bin/env python3
"""
测试文件上传API的脚本
"""
import requests
import os
import base64
from io import BytesIO

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1/airelief"

def test_upload_config():
    """测试获取上传配置"""
    print("🔧 测试获取上传配置...")
    
    try:
        response = requests.get(f"{API_BASE}/upload/config")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 获取配置失败: {e}")
        return False

def create_test_audio_file():
    """创建测试音频文件"""
    # 创建一个简单的WebM音频文件头（模拟）
    webm_header = b'\x1a\x45\xdf\xa3'  # WebM文件头
    audio_data = webm_header + b'\x00' * 1024  # 1KB的测试数据
    
    return BytesIO(audio_data)

def create_test_image_file():
    """创建测试图片文件"""
    # 创建一个简单的PNG文件头（模拟）
    png_header = b'\x89PNG\r\n\x1a\n'  # PNG文件头
    image_data = png_header + b'\x00' * 512  # 512字节的测试数据
    
    return BytesIO(image_data)

def test_upload_audio():
    """测试音频文件上传"""
    print("🎵 测试音频文件上传...")
    print("⚠️ 需要认证，跳过此测试")
    return None

    # 注释掉需要认证的测试
    # try:
    #     # 创建测试音频文件
    #     audio_file = create_test_audio_file()
    #
    #     files = {
    #         'file': ('test_audio.webm', audio_file, 'audio/webm')
    #     }
    #     data = {
    #         'file_type': 'audio'
    #     }
    #
    #     response = requests.post(f"{API_BASE}/upload", files=files, data=data)
    #     print(f"状态码: {response.status_code}")
    #     print(f"响应: {response.json()}")
    #
    #     if response.status_code == 200:
    #         result = response.json()
    #         if result.get('success'):
    #             file_url = result['data']['file_url']
    #             print(f"✅ 音频上传成功: {file_url}")
    #             return file_url
    #
    #     return None
    # except Exception as e:
    #     print(f"❌ 音频上传失败: {e}")
    #     return None

def test_upload_avatar():
    """测试头像文件上传"""
    print("🖼️ 测试头像文件上传...")
    print("⚠️ 需要认证，跳过此测试")
    return None

def test_file_access(file_url):
    """测试文件访问"""
    if not file_url:
        print("⚠️ 没有文件URL，跳过访问测试")
        return False
    
    print(f"📁 测试文件访问: {file_url}")
    
    try:
        response = requests.get(file_url)
        print(f"状态码: {response.status_code}")
        print(f"内容类型: {response.headers.get('content-type')}")
        print(f"文件大小: {len(response.content)} bytes")
        
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 文件访问失败: {e}")
        return False

def test_invalid_file_type():
    """测试无效文件类型"""
    print("❌ 测试无效文件类型...")
    
    try:
        audio_file = create_test_audio_file()
        
        files = {
            'file': ('test.webm', audio_file, 'audio/webm')
        }
        data = {
            'file_type': 'invalid_type'
        }
        
        response = requests.post(f"{API_BASE}/upload", files=files, data=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        # 应该返回400错误
        return response.status_code == 400
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试文件上传API...")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 1. 测试获取配置
    results.append(("获取上传配置", test_upload_config()))
    
    # 2. 测试音频上传
    audio_url = test_upload_audio()
    results.append(("音频文件上传", audio_url is not None))
    
    # 3. 测试头像上传
    avatar_url = test_upload_avatar()
    results.append(("头像文件上传", avatar_url is not None))
    
    # 4. 测试文件访问
    if audio_url:
        results.append(("音频文件访问", test_file_access(audio_url)))
    
    if avatar_url:
        results.append(("头像文件访问", test_file_access(avatar_url)))
    
    # 5. 测试无效文件类型
    results.append(("无效文件类型处理", test_invalid_file_type()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查服务器状态")

if __name__ == "__main__":
    main()
