<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue'

// Props
interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '聊聊你的情绪...',
  disabled: false,
  maxLength: 1000
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'send', message: string): void
  (e: 'start-recording'): void  // 开始录音（传递给父组件处理）
  (e: 'stop-recording'): void   // 停止录音（传递给父组件处理）
  (e: 'cancel-recording'): void // 取消录音（传递给父组件处理）
}

const emit = defineEmits<Emits>()

// 响应式数据
const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})
const inputMode = ref<'text' | 'voice'>('text') // 输入模式：文本或语音
const isRecording = ref(false) // 是否正在录音
const touchStartY = ref(0) // 触摸开始的Y坐标
const isCancelling = ref(false) // 是否正在取消录音

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

// 监听输入值变化
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 切换输入模式
const toggleInputMode = (): void => {
  if (props.disabled) return
  inputMode.value = inputMode.value === 'text' ? 'voice' : 'text'
}

// 发送消息
const handleSend = () => {
  if (inputValue.value.trim()) {
    emit('send', inputValue.value)
    inputValue.value = '' // 清空输入框
  }
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // Enter键发送消息，Shift+Enter换行
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault() // 阻止默认的换行行为
    handleSend()
  }
  // Shift+Enter允许换行，不做任何处理
}

// 开始录音
const startRecording = (event: TouchEvent | MouseEvent): void => {
  if (inputMode.value !== 'voice' || props.disabled) return

  // 记录触摸开始的Y坐标
  if (event instanceof TouchEvent && event.touches.length > 0) {
    touchStartY.value = event.touches[0].clientY
  } else if (event instanceof MouseEvent) {
    touchStartY.value = event.clientY
  }

  isRecording.value = true
  isCancelling.value = false
  emit('start-recording')
}

// 停止录音
const stopRecording = (): void => {
  if (inputMode.value !== 'voice' || !isRecording.value) return

  if (isCancelling.value) {
    // 如果是取消状态，则取消录音
    emit('cancel-recording')
  } else {
    // 否则停止录音并发送
    emit('stop-recording')
  }

  isRecording.value = false
  isCancelling.value = false
}

// 取消录音
const cancelRecording = (): void => {
  if (inputMode.value !== 'voice' || !isRecording.value) return

  isRecording.value = false
  isCancelling.value = false
  emit('cancel-recording')
}

// 处理触摸移动事件
const handleTouchMove = (event: TouchEvent): void => {
  if (!isRecording.value) return

  if (event.touches.length > 0) {
    const currentY = event.touches[0].clientY
    const diffY = touchStartY.value - currentY

    // 如果上滑超过50像素，进入取消状态
    if (diffY > 50) {
      isCancelling.value = true
    } else {
      isCancelling.value = false
    }
  }
}

// 组件挂载时添加事件监听
onMounted(() => {
  document.addEventListener('touchmove', handleTouchMove, { passive: false })
})

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('touchmove', handleTouchMove)
})
</script>

<template>
  <div class="chat-input-wrapper">
    <div class="input-area">
      <div class="input-container">
        <!-- 语音/键盘切换图标 -->
        <div
          v-show="!(inputMode === 'voice' && isRecording)"
          class="voice-icon"
          @click="toggleInputMode"
        >
          <img
            v-if="inputMode === 'text'"
            src="@/assets/icon/chat-voice.svg"
            alt="语音输入"
            class="icon-img"
          />
          <img
            v-else
            src="@/assets/icon/chat-keyboard.svg"
            alt="键盘输入"
            class="icon-img"
          />
        </div>

        <!-- 文本输入模式 -->
        <template v-if="inputMode === 'text'">
          <van-field
            v-model="inputValue"
            type="textarea"
            :placeholder="placeholder"
            class="message-input"
            style="flex-grow: 1;"
            rows="1"
            autosize
            input-align="left"
            :disabled="disabled"
            @keydown="handleKeyDown"
          />
          <van-button
            type="primary"
            size="small"
            :disabled="inputValue.trim().length === 0 || disabled"
            @click="handleSend"
            class="send-button"
          >
            发送
          </van-button>
        </template>

        <!-- 语音输入模式 -->
        <template v-else>
          <div
            class="voice-input-area"
            :class="{ 'recording': isRecording }"
            @touchstart.prevent="startRecording"
            @touchend.prevent="stopRecording"
            @touchcancel.prevent="cancelRecording"
            @mousedown.prevent="startRecording"
            @mouseup.prevent="stopRecording"
            @mouseleave.prevent="cancelRecording"
          >
            <span v-if="!isRecording" class="voice-hint">按住 说话</span>
            <!-- 录音时不显示文字，只保持样式变化，状态信息由ChatPage的遮罩层显示 -->
          </div>
        </template>
      </div>
    </div>
    <div class="safe-area-bottom"></div>
  </div>
</template>

<style scoped>
.chat-input-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  max-width: 600px;
  margin: 0 auto;
  z-index: 10;
}

.input-area {
  background-color: #F6F6F8;
  padding: 8px 16px;
  border-top: 1px solid #F0EEF4;
}

.safe-area-bottom {
  height: 20px;
  background-color: #F6F6F8;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.voice-icon {
  width: 33px;
  height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
}

.icon-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.message-input {
  flex: 1;
}

/* 确保 van-field 自身作为 van-cell 没有额外的垂直 padding */
:deep(.message-input.van-cell) {
  padding: 0 4px !important;
  border-radius: 6px
}

:deep(.van-field__body) {
  background-color: #FFFFFF;
  padding: 7px 6px; /* 上下总 padding: 14px */
  display: flex;
  align-items: flex-start; /* 当文本为单行时，保持其顶部对齐 */
  min-height: 24px;   /* 目标 body 内容高度: 24px。24px (内容) + 14px (padding) = 38px body 总高度 */
  max-height: 120px; /* autosize 的最大高度 */
  box-sizing: border-box; /* 确保 padding 和 border 计算在 min-height/height 内 */
}

:deep(.van-field__control::placeholder) {
  color: #91919E;
  font-family: 'PingFang SC', sans-serif;
  font-style: normal;
  font-weight: 300;
  font-size: 16px;
  line-height: 22px;
}

:deep(.van-field__control[type="textarea"]) {
  word-wrap: break-word;
  word-break: break-all;
}

.send-button {
  background-color: #5F59FF;
  border-color: #5F59FF;
  border-radius: 6px;
  height: 38px;
  font-size: 16px;
  padding: 4px 12px;
}

:deep(.van-button--primary[disabled]) {
  opacity: 0.5;
}

.voice-input-area {
  flex-grow: 1;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background-color: #f5f5f5;
  border-radius: 18px;
  color: #666;
  font-size: 14px;
  user-select: none;
  -webkit-user-select: none;
  touch-action: none;
}

.voice-input-area.recording {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.recording-hint {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
  padding: 4px 0;
}

.recording-text {
  font-weight: 500;
}

.cancel-text {
  font-size: 12px;
  opacity: 0.8;
}
</style>
